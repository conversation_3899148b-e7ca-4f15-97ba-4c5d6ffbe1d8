# ParameterDefinition创建功能说明

## 功能概述

在创建执行工步后，系统会自动查询ProcessStep下的stepParameter对象，并根据workStep及stepParameter创建ParameterDefinition对象。

## 实现位置

文件：`craft/src/main/java/com/bzlj/craft/transform/handle/TaskTransformHandle.java`

## 主要修改

### 1. 新增依赖注入

```java
/**
 * 工步参数仓储
 */
@Autowired
private StepParameterRepository stepParameterRepository;

/**
 * 参数定义仓储
 */
@Autowired
private ParameterDefinitionRepository parameterDefinitionRepository;
```

### 2. 修改saveWorkSteps方法

原方法只创建和保存WorkStep，现在增加了创建ParameterDefinition的功能：

```java
private List<WorkStep> saveWorkSteps(List<ProcessStep> steps, ProductionTask task){
    // 创建工作步骤
    List<WorkStep> workSteps = steps.stream().map(step -> {
        WorkStep workStep = new WorkStep();
        workStep.setStep(step);
        workStep.setTask(task);
        workStep.setWorkStepName(step.getStepName());
        workStep.setWorkStepOrder(step.getStepOrder());
        return workStep;
    }).toList();
    
    // 保存工作步骤
    List<WorkStep> savedWorkSteps = workStepRepository.saveAll(workSteps);
    
    // 为每个工作步骤创建参数定义
    createParameterDefinitions(savedWorkSteps);
    
    return savedWorkSteps;
}
```

### 3. 新增createParameterDefinitions方法

```java
/**
 * 为工作步骤创建参数定义
 * <p>
 * 查询ProcessStep下的stepParameter对象，
 * 根据workStep及stepParameter创建ParameterDefinition对象
 * </p>
 *
 * @param workSteps 工作步骤列表
 */
private void createParameterDefinitions(List<WorkStep> workSteps) {
    List<ParameterDefinition> parameterDefinitions = new ArrayList<>();
    
    for (WorkStep workStep : workSteps) {
        // 查询ProcessStep下的stepParameter对象
        List<StepParameter> stepParameters = stepParameterRepository
                .findByStepIdOrderByCreatedTime(workStep.getStep().getId());
        
        if (!CollectionUtils.isEmpty(stepParameters)) {
            // 根据workStep及stepParameter创建ParameterDefinition对象
            for (StepParameter stepParameter : stepParameters) {
                ParameterDefinition parameterDefinition = new ParameterDefinition();
                parameterDefinition.setWorkStep(workStep);
                parameterDefinition.setParamName(stepParameter.getParamName());
                parameterDefinition.setParamCodee(stepParameter.getParamCode());
                parameterDefinition.setParamType(stepParameter.getParamType());
                
                parameterDefinitions.add(parameterDefinition);
            }
        }
    }
    
    // 批量保存参数定义
    if (!CollectionUtils.isEmpty(parameterDefinitions)) {
        parameterDefinitionRepository.saveAll(parameterDefinitions);
        log.info("为任务 {} 创建了 {} 个参数定义", 
                workSteps.get(0).getTask().getTaskCode(), 
                parameterDefinitions.size());
    }
}
```

## 执行流程

1. **创建任务时**：调用`transform`方法处理任务分发JSON
2. **创建工作步骤**：调用`saveWorkSteps`方法创建WorkStep
3. **保存工作步骤**：将WorkStep保存到数据库
4. **查询工步参数**：根据ProcessStep的ID查询对应的StepParameter列表
5. **创建参数定义**：为每个StepParameter创建对应的ParameterDefinition
6. **设置关联关系**：将WorkStep与ParameterDefinition关联
7. **批量保存**：将所有ParameterDefinition保存到数据库

## 数据映射关系

| StepParameter字段 | ParameterDefinition字段 | 说明 |
|------------------|------------------------|------|
| paramName        | paramName              | 参数名称 |
| paramCode        | paramCodee             | 参数代码 |
| paramType        | paramType              | 参数类型 |
| -                | workStep               | 关联的工作步骤 |

## 日志输出

成功创建参数定义后，会输出日志：
```
为任务 {taskCode} 创建了 {count} 个参数定义
```

## 注意事项

1. 只有当ProcessStep下存在StepParameter时，才会创建ParameterDefinition
2. 使用批量保存提高性能
3. 保持事务一致性，如果创建失败会回滚整个任务创建过程
4. ParameterDefinition的其他字段（如unit、minValue、maxValue等）可根据需要进一步扩展
