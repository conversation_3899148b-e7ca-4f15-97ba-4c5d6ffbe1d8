package com.bzlj.craft.transform.handle;

import com.bzlj.craft.entity.*;
import com.bzlj.craft.repository.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * TaskTransformHandle测试类
 * 测试创建执行工步后自动创建ParameterDefinition的功能
 */
@ExtendWith(MockitoExtension.class)
public class TaskTransformHandleTest {

    @Mock
    private StepParameterRepository stepParameterRepository;
    
    @Mock
    private ParameterDefinitionRepository parameterDefinitionRepository;
    
    @Mock
    private WorkStepRepository workStepRepository;
    
    @InjectMocks
    private TaskTransformHandle taskTransformHandle;

    @Test
    public void testCreateParameterDefinitions() {
        // 准备测试数据
        ProductionTask task = new ProductionTask();
        task.setTaskCode("TEST_TASK_001");
        
        ProcessStep processStep = new ProcessStep();
        processStep.setId("step_001");
        processStep.setStepName("测试工步");
        
        WorkStep workStep = new WorkStep();
        workStep.setWorkStepId("work_step_001");
        workStep.setStep(processStep);
        workStep.setTask(task);
        workStep.setWorkStepName("测试工作步骤");
        
        // 创建StepParameter测试数据
        StepParameter stepParam1 = new StepParameter();
        stepParam1.setParamId("param_001");
        stepParam1.setParamCode("TEMP_001");
        stepParam1.setParamName("温度参数");
        stepParam1.setStep(processStep);
        
        StepParameter stepParam2 = new StepParameter();
        stepParam2.setParamId("param_002");
        stepParam2.setParamCode("PRESS_001");
        stepParam2.setParamName("压力参数");
        stepParam2.setStep(processStep);
        
        List<StepParameter> stepParameters = Arrays.asList(stepParam1, stepParam2);
        
        // Mock repository行为
        when(stepParameterRepository.findByStepIdOrderByCreatedTime("step_001"))
                .thenReturn(stepParameters);
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = TaskTransformHandle.class
                    .getDeclaredMethod("createParameterDefinitions", List.class);
            method.setAccessible(true);
            method.invoke(taskTransformHandle, Arrays.asList(workStep));
            
            // 验证parameterDefinitionRepository.saveAll被调用
            verify(parameterDefinitionRepository, times(1)).saveAll(any(List.class));
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
